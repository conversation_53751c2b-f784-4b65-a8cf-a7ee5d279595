{"Send from": "Send from", "Home": "Home", "How it work": "How it work", "Help": "Help", "Contact": "Contact", "About Us": "About Us", "Blog": "Blog", "FAQ": "FAQ", "Sign In": "Sign In", "Sign Up": "Sign Up", "My Profile": "My Profile", "Profile Settings": "Profile Settings", "About Company": "About Company", "Useful Links": "Useful Links", "All Right Reserved.": "All Right Reserved.", "Subscribe": "Subscribe", "Transfer Log": "Transfer Log", "Payment History": "Payment History", "Transaction": "Transaction", "Payment Log": "Payment Log", "Support": "Support", "Support Ticket": "Support Ticket", "2FA Security": "2FA Security", "Logout": "Logout", "You have no notifications": "You have no notifications", "Clear": "Clear", "Payment Hold": "Payment Hold", "Amount": "Amount", "Status": "Status", "Pending": "Pending", "Rejected": "Rejected", "Completed": "Completed", "Close": "Close", "Accept": "Accept", "Reject": "Reject", "Your Card Information": "Your Card Information", "Card Number": "Card Number", "Name On Your Card": "Name On Your Card", "Payment Preview": "Payment Preview", "PLEASE SEND EXACTLY": "PLEASE SEND EXACTLY", "SCAN TO SEND": "SCAN TO SEND", "Please Pay": "Please Pay", "To Get": "To Get", "Pay Now": "Pay Now", "Pay via Monnify": "Pay via Monnify", "Pending Payment": "Pending Payment", "Complete Payment": "Complete Payment", "Rejected Payment": "Rejected Payment", "Search": "Search", "Transaction ID": "Transaction ID", "Gateway": "Gateway", "Charge": "Charge", "Time": "Time", "Detail": "Detail", "Complete": "Complete", "Cancel": "Cancel", "No Data Found!": "No Data Found!", "Transactions": "Transactions", "Admin Feedback": "<PERSON><PERSON>", "Payment Information": "Payment Information", "Request Amount": "Request Amount", "Charge Amount": "Charge Amount", "Total Payable": "Total Payable", "Confirm Now": "Confirm Now", "Image Update": "Image Update", "Profile Information": "Profile Information", "Password Setting": "Password Setting", "First Name": "First Name", "Last Name": "Last Name", "Username": "Username", "Email Address": "Email Address", "Phone Number": "Phone Number", "Language": "Language", "Preferred language": "Preferred language", "Select Language": "Select Language", "Password": "Password", "Address": "Address", "Update User": "Update User", "Current Password": "Current Password", "New Password": "New Password", "Forget Password": "Forget Password", "Confirm Password": "Confirm Password", "Update Password": "Update Password", "Reset Password": "Reset Password", "Send Password Reset Link": "Send Password Reset Link", "Don't have any account?": "Don't have any account?", "Create Ticket": "Create Ticket", "Last Reply": "Last Reply", "Action": "Action", "Open": "Open", "Answered": "Answered", "Replied": "Replied", "Closed": "Closed", "Customer Reply": "Customer Reply", "Image Upload": "Image Upload", "Upload File": "Upload File", "Reply": "Reply", "Are you want to close ticket?": "Are you want to close ticket?", "Confirm": "Confirm", "All Payment": "All Payment", "Cancel Payment": "Cancel Payment", "Search for Transaction ID": "Search for Transaction ID", "Remark": "Remark", "SL No.": "SL No.", "Two Factor Authenticator": "Two Factor Authenticator", "Deactive Two Factor": "Deactive Two Factor", "Active Two Factor": "Active Two Factor", "Google Authenticator": "Google Authenticator", "Use Google Authenticator to Scan the QR code  or use the code": "Use Google Authenticator to Scan the QR code  or use the code", "Google Authenticator is a multifactor app for mobile devices. It generates timed codes used during the 2-step verification process. To use Google Authenticator, install the Google Authenticator application on your mobile device.": "Google Authenticator is a multifactor app for mobile devices. It generates timed codes used during the 2-step verification process. To use Google Authenticator, install the Google Authenticator application on your mobile device.", "DOWNLOAD APP": "DOWNLOAD APP", "Payment By": "Payment By", "Payable": "Payable", "Email Verification": "Email Verification", "Code": "Code", "Email Or Username": "Email Or Username", "Forgot password?": "Forgot password?", "Already have an account?": "Already have an account?", "Enter Code": "Enter Code", "Didn't get Code? Click to": "Didn't get Code? Click to", "Resend code": "Resend code", "Updated Successfully.": "Updated Successfully.", "Password Changes successfully.": "Password Changes successfully.", "Current password did not match": "Current password did not match", "Two Factor has been enabled.": "Two Factor has been enabled.", "Two Factor has been disabled.": "Two <PERSON> has been disabled.", "Wrong Verification Code.": "Wrong Verification Code.", "Image could not be uploaded.": "Image could not be uploaded.", "Invalid Request!": "Invalid Request!", "Payment has been released.": "Payment has been released.", "Your report has been sent.": "Your report has been sent.", "Email verification code has been sent": "Email verification code has been sent", "SMS verification code has been sent": "SMS verification code has been sent", "Sending Failed": "Sending Failed", "Email verification code is required": "Email verification code is required", "Verification code didn't match!": "Verification code didn't match!", "Wrong Verification Code": "Wrong Verification Code", "Blog Details": "Blog Details", "Contact Us": "Contact Us", "Your Name": "Your Name", "Your Email": "Your Email", "Your Subject": "Your Subject", "Message": "Message", "Send Message": "Send Message", "Forbidden": "Forbidden", "You don't have permission to access ‘/’ on this server": "You don't have permission to access ‘/’ on this server", "Back To Home": "Back To Home", "Opps!": "Opps!", "The page you are looking for was not found.": "The page you are looking for was not found.", "Method Not Allowed": "Method Not Allowed", "Sorry, your session has expired": "Sorry, your session has expired", "Internal Server Error": "Internal Server Error", "The server encountered an internal error misconfiguration and was unable to complate your request. Please contact the server administrator.": "The server encountered an internal error misconfiguration and was unable to complate your request. Please contact the server administrator.", "Previous": "Previous", "Next": "Next", "Download On": "Download On", "App Store": "App Store", "Google Play": "Google Play", "Continue": "Continue", "Add Recipient": "Add Recipient", "Recipient Name": "Recipient Name", "Recipient Contact No.": "Recipient Contact No.", "Recipient Email Address": "Recipient Email Address", "Source Of Fund": "Source Of Fund", "Sending Purpose": "Sending Purpose", "Select One": "Select One", "Fees": "Fees", "Exchange rate": "Exchange rate", "You pay in total": "You pay in total", "Your recipient gets": "Your recipient gets", "Get a promo code?": "Get a promo code?", "Apply": "Apply", "Invalid promo code": "Invalid promo code", "Select Country": "Select Country", "SEND": "SEND", "RECEIVE": "RECEIVE", "SL": "SL", "Invoice": "Invoice", "Recipient": "Recipient", "Send Amount": "Send Amount", "Send At": "Send At", "Receive At": "Receive At", "Rate": "Rate", "Information Need": "Information Need", "Cancelled": "Cancelled", "Processing": "Processing", "Fill Up Form": "Fill Up Form", "Pay Payment": "Pay Payment", "Details": "Details", "Payment Failed": "Payment Failed", "READ MORE": "READ MORE", "By Admin": "By Admin", "Fee Depend on your service": "Fee Depend on your service", "Remove": "Remove", "Change": "Change", "Select": "Select", "Joined At": "Joined At", "Subject": "Subject", "Provider must be required": "Provider must be required", "Receiver Country Not Found": "Receiver Country Not Found", "Receiver Country Service Not Available": "Receiver Country Service Not Available", "Sender country is required": "Sender country is required", "Please select a currency to receive": "Please select a currency to receive", "Service is required": "Service is required", "Enter Amount": "Enter Amount", "Sender Country Not Found": "Sender Country Not Found", "You are not eligible to change request.": "You are not eligible to change request.", "RECIPIENT DETAILS": "RECIPIENT DETAILS", "TRANSFER LOG": "TRANSFER LOG", "Invalid Payment Request": "Invalid Payment Request", "Payment has been completed": "Payment has been completed", "Payment has been rejected": "Payment has been rejected", "Wait for payment approval by admin": "Wait for payment approval by admin", "Only png, jpg, jpeg images are allowed": "Only png, jpg, jpeg images are allowed", "First Name field is required": "First Name field is required", "Last Name field is required": "Last Name field is required", "Mail has been sent": "Mail has been sent", "Subscribe successfully": "Subscribe successfully", "The email field is required.": "The email field is required.", "N/A": "N/A", "Dashboard": "Dashboard", "Hello,": "Hello,", "Balance": "Balance", "Profile": "Profile", "Your Assigned Country": "Your Assigned Country", "You can only process payouts for transactions to this country": "You can only process payouts for transactions to this country", "Not Assigned": "Not Assigned", "Earn From Payout Money": "Earn From Payout Money", "Total Ticket": "Total Ticket", "My Recipients": "My Recipients", "Switching...": "Switching...", "English": "English", "Arabic": "Arabic", "Language switched to": "Language switched to", "Failed to switch language. Please try again.": "Failed to switch language. Please try again.", "Transfer": "Transfer", "Send Money": "Send Money", "Transfer to Agent": "Transfer to Agent", "Agent Transfer History": "Agent Transfer History", "Payout Remittance": "Payout Remittance", "Payout Money": "Payout Money", "Payout Log": "Payout Log", "Deposit": "<PERSON><PERSON><PERSON><PERSON>", "Add Fund": "Add Fund", "Fund Log": "Fund Log", "LOG": "LOG", "Withdraw": "Withdraw", "Withdraw Log": "Withdraw Log", "Support Tickets": "Support Tickets", "Payout Form": "Payout Form", "Invoice Number": "Invoice Number", "Submit": "Submit", "Name": "Name", "Email": "Email", "Contact No": "Contact No", "My Balance": "My Balance", "Total Send Money": "Total Send Money", "Total Payout": "Total Payout", "Total Deposit": "Total Deposit", "Total Transaction": "Total Transaction"}