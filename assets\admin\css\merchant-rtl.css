/*==========================================*/
/*         MERCHANT DASHBOARD RTL STYLES   */
/*==========================================*/

/* RTL Layout Base Styles */
.rtl-layout {
    direction: rtl;
    text-align: right;
}

/* Sidebar RTL Adjustments */
.rtl-layout .left-sidebar {
    right: 0;
    left: auto;
}

.rtl-layout .page-wrapper {
    margin-right: 250px;
    margin-left: 0;
}

.rtl-layout .sidebar-nav ul li a {
    text-align: right;
}

.rtl-layout .sidebar-nav ul li a i {
    margin-left: 10px;
    margin-right: 0;
}

.rtl-layout .hide-menu {
    margin-right: 0;
    margin-left: 10px;
}

/* Header RTL Adjustments */
.rtl-layout .navbar-nav.float-right {
    float: left !important;
}

.rtl-layout .navbar-nav.float-left {
    float: right !important;
}

.rtl-layout .ml-auto {
    margin-left: 0 !important;
    margin-right: auto !important;
}

.rtl-layout .mr-auto {
    margin-right: 0 !important;
    margin-left: auto !important;
}

.rtl-layout .ml-1 { margin-left: 0 !important; margin-right: 0.25rem !important; }
.rtl-layout .ml-2 { margin-left: 0 !important; margin-right: 0.5rem !important; }
.rtl-layout .ml-3 { margin-left: 0 !important; margin-right: 1rem !important; }

.rtl-layout .mr-1 { margin-right: 0 !important; margin-left: 0.25rem !important; }
.rtl-layout .mr-2 { margin-right: 0 !important; margin-left: 0.5rem !important; }
.rtl-layout .mr-3 { margin-right: 0 !important; margin-left: 1rem !important; }

.rtl-layout .pl-1 { padding-left: 0 !important; padding-right: 0.25rem !important; }
.rtl-layout .pl-2 { padding-left: 0 !important; padding-right: 0.5rem !important; }
.rtl-layout .pl-3 { padding-left: 0 !important; padding-right: 1rem !important; }

.rtl-layout .pr-1 { padding-right: 0 !important; padding-left: 0.25rem !important; }
.rtl-layout .pr-2 { padding-right: 0 !important; padding-left: 0.5rem !important; }
.rtl-layout .pr-3 { padding-right: 0 !important; padding-left: 1rem !important; }

/* Dropdown RTL Adjustments */
.rtl-layout .dropdown-menu-right {
    right: auto !important;
    left: 0 !important;
}

.rtl-layout .dropdown-menu {
    text-align: right;
}

/* Card and Content RTL Adjustments */
.rtl-layout .card-body {
    text-align: right;
}

.rtl-layout .d-flex {
    direction: rtl;
}

.rtl-layout .text-truncate {
    text-align: right;
}

/* Form RTL Adjustments */
.rtl-layout .form-control {
    text-align: right;
}

.rtl-layout .form-group label {
    text-align: right;
}

.rtl-layout .input-group-prepend {
    order: 2;
}

.rtl-layout .input-group-append {
    order: 0;
}

/* Table RTL Adjustments */
.rtl-layout table {
    direction: rtl;
}

.rtl-layout th,
.rtl-layout td {
    text-align: right;
}

/* Button RTL Adjustments */
.rtl-layout .btn {
    text-align: center;
}

.rtl-layout .btn i {
    margin-left: 5px;
    margin-right: 0;
}

/* Breadcrumb RTL Adjustments */
.rtl-layout .breadcrumb {
    direction: rtl;
}

.rtl-layout .breadcrumb-item + .breadcrumb-item::before {
    content: "\\";
    transform: scaleX(-1);
}

/* Navigation RTL Adjustments */
.rtl-layout .nav-toggler {
    right: auto;
    left: 15px;
}

/* Notification RTL Adjustments */
.rtl-layout .notify-no {
    right: auto;
    left: -5px;
}

/* Select2 RTL Adjustments */
.rtl-layout .select2-container--default .select2-selection--single {
    text-align: right;
}

.rtl-layout .select2-container--default .select2-selection--single .select2-selection__arrow {
    left: 1px;
    right: auto;
}

/* Badge RTL Adjustments */
.rtl-layout .badge {
    text-align: center;
}

/* Icon RTL Adjustments */
.rtl-layout .feather-icon,
.rtl-layout .svg-icon {
    margin-left: 5px;
    margin-right: 0;
}

/* Responsive RTL Adjustments */
@media (max-width: 767px) {
    .rtl-layout .page-wrapper {
        margin-right: 0;
        margin-left: 0;
    }
    
    .rtl-layout .left-sidebar {
        right: -250px;
        left: auto;
    }
    
    .rtl-layout .left-sidebar.show {
        right: 0;
    }
}

/* Custom RTL Fixes for Specific Elements */
.rtl-layout .width-40p {
    margin-left: 10px;
    margin-right: 0;
}

.rtl-layout .user-dd {
    right: auto !important;
    left: 0 !important;
}

.rtl-layout .animated.flipInY {
    animation-name: flipInY;
}

/* Fix for language dropdown positioning */
.rtl-layout .navbar-nav .dropdown-menu {
    right: 0;
    left: auto;
}

/* Arabic Font Support */
.rtl-layout {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Ensure proper text alignment for Arabic */
.rtl-layout h1, .rtl-layout h2, .rtl-layout h3, 
.rtl-layout h4, .rtl-layout h5, .rtl-layout h6 {
    text-align: right;
}

.rtl-layout p, .rtl-layout span, .rtl-layout div {
    text-align: right;
}

/* Fix for dashboard cards */
.rtl-layout .admin-fa_icon .card-body .d-flex {
    flex-direction: row-reverse;
}

.rtl-layout .admin-fa_icon .card-body .d-flex > div:first-child {
    margin-left: auto;
    margin-right: 0;
}

/* Fix for statistics cards alignment */
.rtl-layout .d-lg-flex.d-md-block.align-items-center {
    flex-direction: row-reverse;
}

.rtl-layout .mt-md-3.mt-lg-0 {
    margin-left: 0;
    margin-right: auto;
}

/* Additional sidebar fixes */
.rtl-layout .sidebar-nav ul li a .hide-menu {
    text-align: right;
}

.rtl-layout .left-sidebar .scroll-sidebar {
    direction: rtl;
}

/* Fix for main wrapper in RTL */
.rtl-layout #main-wrapper[data-sidebartype="full"] .page-wrapper {
    margin-right: 250px;
    margin-left: 0;
}

/* Fix for mobile sidebar */
@media (max-width: 767px) {
    .rtl-layout #main-wrapper[data-sidebartype="full"] .left-sidebar {
        right: -250px;
        left: auto;
    }

    .rtl-layout #main-wrapper[data-sidebartype="full"] .left-sidebar.show {
        right: 0;
    }
}

/* Fix for navbar brand */
.rtl-layout .navbar-brand {
    margin-left: 0;
    margin-right: 1rem;
}

/* Fix for topbar */
.rtl-layout .topbar .navbar-header {
    float: right;
}

/* Fix for page breadcrumb */
.rtl-layout .page-breadcrumb {
    text-align: right;
}

.rtl-layout .page-breadcrumb .breadcrumb {
    justify-content: flex-end;
}

/* Fix for card titles and content */
.rtl-layout .card-title {
    text-align: right;
}

.rtl-layout .text-muted {
    text-align: right;
}
